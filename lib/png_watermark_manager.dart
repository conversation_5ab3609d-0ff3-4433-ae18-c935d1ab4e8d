import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;

class PngWatermark {
  final String url;
  final String name;
  Uint8List? imageData;
  ui.Image? uiImage;
  img.Image? processedImage;
  Size? originalSize;

  // 新增：缩放、旋转和镜像属性
  double scale;
  double rotation; // 旋转角度（弧度）
  bool flipHorizontal; // 水平镜像翻转
  bool flipVertical; // 垂直镜像翻转

  PngWatermark({
    required this.url,
    required this.name,
    this.scale = 1.0,
    this.rotation = 0.0,
    this.flipHorizontal = false,
    this.flipVertical = false,
  });
}

class PngWatermarkManager {
  static final PngWatermarkManager _instance = PngWatermarkManager._internal();
  factory PngWatermarkManager() => _instance;
  PngWatermarkManager._internal();

  final List<PngWatermark> _watermarks = [
    PngWatermark(
      url: 'https://oss-hel.vup.tools/img/123-456-789.png',
      name: '水印1',
    ),
    PngWatermark(
      url: 'https://oss-hel.vup.tools/img/987-654-321.png',
      name: '水印2',
    ),
  ];

  PngWatermark? _selectedWatermark;
  bool _isPreviewMode = false;

  List<PngWatermark> get watermarks => _watermarks;
  PngWatermark? get selectedWatermark => _selectedWatermark;
  bool get isPreviewMode => _isPreviewMode;

  void setSelectedWatermark(PngWatermark? watermark) {
    _selectedWatermark = watermark;
  }

  void setPreviewMode(bool enabled) {
    _isPreviewMode = enabled;
  }

  // 更新选中水印的缩放
  void updateWatermarkScale(double scale) {
    if (_selectedWatermark != null) {
      _selectedWatermark!.scale = scale;
    }
  }

  // 更新选中水印的旋转角度
  void updateWatermarkRotation(double rotation) {
    if (_selectedWatermark != null) {
      _selectedWatermark!.rotation = rotation;
    }
  }

  // 更新选中水印的水平镜像翻转
  void updateWatermarkFlipHorizontal(bool flip) {
    if (_selectedWatermark != null) {
      _selectedWatermark!.flipHorizontal = flip;
    }
  }

  // 更新选中水印的垂直镜像翻转
  void updateWatermarkFlipVertical(bool flip) {
    if (_selectedWatermark != null) {
      _selectedWatermark!.flipVertical = flip;
    }
  }

  Future<bool> loadWatermark(PngWatermark watermark) async {
    try {
      print('🖼️ 开始加载PNG水印: ${watermark.url}');

      final response = await http.get(Uri.parse(watermark.url));
      if (response.statusCode == 200) {
        watermark.imageData = response.bodyBytes;

        // 解码为ui.Image用于Flutter显示
        final codec = await ui.instantiateImageCodec(watermark.imageData!);
        final frame = await codec.getNextFrame();
        watermark.uiImage = frame.image;
        watermark.originalSize = Size(
          frame.image.width.toDouble(),
          frame.image.height.toDouble(),
        );

        // 解码为img.Image用于图像处理
        watermark.processedImage = img.decodeImage(watermark.imageData!);

        print('✅ PNG水印加载成功: ${watermark.name} (${watermark.originalSize})');
        return true;
      } else {
        print('❌ PNG水印加载失败: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('❌ PNG水印加载错误: $e');
      return false;
    }
  }

  Future<void> loadAllWatermarks() async {
    print('🖼️ 开始加载所有PNG水印...');
    for (final watermark in _watermarks) {
      await loadWatermark(watermark);
    }
    print('✅ 所有PNG水印加载完成');
  }

  // 计算水印在屏幕上的显示尺寸和位置（新的排版规则）
  Rect calculateWatermarkRect(Size screenSize, PngWatermark watermark) {
    if (watermark.originalSize == null) {
      return Rect.zero;
    }

    final originalSize = watermark.originalSize!;
    final aspectRatio = originalSize.width / originalSize.height;

    double width, height;
    double left = 0, top = 0;

    if (aspectRatio > 1) {
      // 横向图片：按照把水印的宽度放到与相机画面一样的尺寸
      width = screenSize.width;
      height = width / aspectRatio;

      // 垂直居中
      top = (screenSize.height - height) / 2;
    } else {
      // 竖向图片：按照把水印的高度放到与相机画面一样的尺寸
      height = screenSize.height;
      width = height * aspectRatio;

      // 水平居中
      left = (screenSize.width - width) / 2;
    }

    return Rect.fromLTWH(left, top, width, height);
  }

  // 计算相机预览区域（完整显示相机画面）
  Rect calculateCameraPreviewRect(Size screenSize, PngWatermark? watermark) {
    // 始终返回全屏尺寸，让相机完整显示
    return Rect.fromLTWH(0, 0, screenSize.width, screenSize.height);
  }

  // 将PNG水印叠加到图像上（新的排版规则，支持缩放、旋转和镜像翻转）
  img.Image? applyWatermarkToImage(
    img.Image originalImage,
    PngWatermark watermark, {
    Size? previewSize, // 预览区域尺寸，用于计算比例
  }) {
    if (watermark.processedImage == null) {
      print('⚠️ PNG水印未加载，无法应用到图像');
      return originalImage;
    }

    try {
      // 获取水印原始尺寸
      img.Image processedWatermark = watermark.processedImage!;

      // 应用镜像翻转
      if (watermark.flipHorizontal) {
        processedWatermark = img.flipHorizontal(processedWatermark);
      }
      if (watermark.flipVertical) {
        processedWatermark = img.flipVertical(processedWatermark);
      }

      final watermarkAspectRatio =
          processedWatermark.width / processedWatermark.height;
      final imageAspectRatio = originalImage.width / originalImage.height;

      int baseWidth, baseHeight;

      // 新的排版规则：
      // 竖向图片：水印高度匹配相机画面高度
      // 横向图片：水印宽度匹配相机画面宽度
      if (watermarkAspectRatio > 1) {
        // 横向水印：宽度匹配相机画面宽度
        baseWidth = originalImage.width;
        baseHeight = (baseWidth / watermarkAspectRatio).round();
      } else {
        // 竖向水印：高度匹配相机画面高度
        baseHeight = originalImage.height;
        baseWidth = (baseHeight * watermarkAspectRatio).round();
      }

      // 应用用户设置的缩放
      final finalWidth = (baseWidth * watermark.scale).round();
      final finalHeight = (baseHeight * watermark.scale).round();

      // 调整水印尺寸
      img.Image resizedWatermark = img.copyResize(
        processedWatermark,
        width: finalWidth,
        height: finalHeight,
      );

      // 如果有旋转角度，应用旋转
      if (watermark.rotation != 0.0) {
        final rotationDegrees = watermark.rotation * 180 / 3.14159; // 弧度转角度
        resizedWatermark = img.copyRotate(
          resizedWatermark,
          angle: rotationDegrees,
        );
      }

      // 计算居中位置
      final offsetX = (originalImage.width - resizedWatermark.width) ~/ 2;
      final offsetY = (originalImage.height - resizedWatermark.height) ~/ 2;

      // 叠加水印
      img.compositeImage(
        originalImage,
        resizedWatermark,
        dstX: offsetX,
        dstY: offsetY,
      );

      print(
        '✅ PNG水印已应用到图像 (缩放: ${watermark.scale}, 旋转: ${watermark.rotation}, 水平翻转: ${watermark.flipHorizontal}, 垂直翻转: ${watermark.flipVertical})',
      );
      return originalImage;
    } catch (e) {
      print('❌ 应用PNG水印到图像时出错: $e');
      return originalImage;
    }
  }
}
