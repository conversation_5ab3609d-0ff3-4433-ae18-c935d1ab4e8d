import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:image/image.dart' as img;
import 'package:intl/intl.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:image_picker/image_picker.dart';
import 'gallery_page.dart';
import 'png_watermark_manager.dart';
import 'png_menu_page.dart';

List<CameraDescription> cameras = [];

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 相机列表将在权限获取成功后获取
  print('🚀 应用启动 - 开始初始化');

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '透卡相机',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const CameraScreen(),
    );
  }
}

class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen>
    with WidgetsBindingObserver {
  CameraController? _controller;
  bool _isInitialized = false;
  bool _isTakingPicture = false;
  final ImagePicker _picker = ImagePicker();
  bool _isAppInBackground = false;
  final PngWatermarkManager _watermarkManager = PngWatermarkManager();
  bool _isLoadingMenu = false;

  // 摄像头切换相关变量
  int _currentCameraIndex = 0; // 当前使用的摄像头索引（0=后置，1=前置）
  bool _isSwitchingCamera = false;

  // 手势控制相关变量
  double _initialScale = 1.0;
  double _initialRotation = 0.0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _requestPermissions();
  }

  Future<void> _requestPermissions() async {
    print('📱 开始权限检查流程');

    // 获取设备信息
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      print(
        '📱 Android设备信息: API ${androidInfo.version.sdkInt}, 型号: ${androidInfo.model}',
      );
    }

    // 检查当前相机权限状态
    final currentCameraStatus = await Permission.camera.status;
    print('📷 当前相机权限状态: $currentCameraStatus');

    // 请求相机权限
    print('📷 正在请求相机权限...');
    final cameraStatus = await Permission.camera.request();
    print('📷 相机权限请求结果: $cameraStatus');

    // 对于Android 10及以上，需要特殊处理存储权限
    bool storageGranted = false;
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      print('💾 开始处理存储权限 (Android API ${androidInfo.version.sdkInt})');

      if (androidInfo.version.sdkInt >= 30) {
        // Android 11及以上：优先检查基本存储权限，MANAGE_EXTERNAL_STORAGE为可选
        print('💾 Android 11+: 检查存储权限');

        // 首先检查基本的存储权限
        final writeStatus = await Permission.storage.status;
        print('💾 WRITE_EXTERNAL_STORAGE权限状态: $writeStatus');

        if (writeStatus.isGranted) {
          storageGranted = true;
          print('💾 基本存储权限已授予');
        } else {
          // 尝试请求基本存储权限
          final requestedWriteStatus = await Permission.storage.request();
          print('💾 请求WRITE_EXTERNAL_STORAGE权限结果: $requestedWriteStatus');

          if (requestedWriteStatus.isGranted) {
            storageGranted = true;
          } else {
            // 基本权限未授予，检查MANAGE_EXTERNAL_STORAGE
            final manageStorageStatus =
                await Permission.manageExternalStorage.status;
            print('💾 MANAGE_EXTERNAL_STORAGE权限状态: $manageStorageStatus');

            if (manageStorageStatus.isGranted) {
              storageGranted = true;
              print('💾 MANAGE_EXTERNAL_STORAGE权限已授予');
            } else {
              // 即使没有完整的存储权限，也允许使用应用私有目录
              print('💾 存储权限未完全授予，将使用应用私有目录');
              storageGranted = true; // 允许继续使用
            }
          }
        }
      } else if (androidInfo.version.sdkInt >= 29) {
        // Android 10使用Scoped Storage，不需要存储权限
        print('💾 Android 10: 使用Scoped Storage，无需额外存储权限');
        storageGranted = true;
      } else {
        // Android 9及以下需要存储权限
        print('💾 Android 9及以下: 请求传统存储权限');
        final currentStorageStatus = await Permission.storage.status;
        print('💾 当前存储权限状态: $currentStorageStatus');

        final storageStatus = await Permission.storage.request();
        print('💾 存储权限请求结果: $storageStatus');
        storageGranted = storageStatus.isGranted;
      }
    } else {
      // iOS需要相册权限
      print('💾 iOS: 请求相册权限');
      final currentPhotosStatus = await Permission.photos.status;
      print('💾 当前相册权限状态: $currentPhotosStatus');

      final photosStatus = await Permission.photos.request();
      print('💾 相册权限请求结果: $photosStatus');
      storageGranted = photosStatus.isGranted;
    }

    // 权限检查结果汇总
    print('✅ 权限检查完成:');
    print('   📷 相机权限: ${cameraStatus.isGranted ? "已授权" : "未授权"}');
    print('   💾 存储权限: ${storageGranted ? "已授权" : "未授权"}');

    if (cameraStatus.isGranted && storageGranted) {
      print('🎉 所有权限已获得，开始初始化相机');
      _initializeCamera();
    } else if (cameraStatus.isGranted && !storageGranted) {
      // 相机权限已授予，但存储权限未授予 - 仍然可以使用相机功能
      print('⚠️ 相机权限已授予，存储权限未授予，但仍可使用基本功能');
      _initializeCamera(); // 继续初始化相机
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('存储权限未授予，照片将保存到应用私有目录'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } else if (cameraStatus.isPermanentlyDenied) {
      // 相机权限被永久拒绝，必须引导用户去设置
      print('❌ 相机权限被永久拒绝，需要用户手动开启');
      if (mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) => AlertDialog(
            title: const Text('需要相机权限'),
            content: const Text('应用需要相机权限才能拍照，请在设置中开启相机权限'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  openAppSettings();
                },
                child: const Text('去设置'),
              ),
            ],
          ),
        );
      }
    } else {
      // 相机权限被临时拒绝，可以再次请求
      print('⚠️ 相机权限被拒绝，但可以再次请求');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('需要相机权限才能使用拍照功能')));
      }
    }
  }

  Future<void> _initializeCamera() async {
    try {
      print('📷 开始获取可用相机列表...');
      cameras = await availableCameras();
      print('📷 发现 ${cameras.length} 个相机设备');

      for (int i = 0; i < cameras.length; i++) {
        print('   相机 $i: ${cameras[i].name} (${cameras[i].lensDirection})');
      }

      if (cameras.isEmpty) {
        print('❌ 没有发现可用的相机设备');
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('没有发现可用的相机设备')));
        }
        return;
      }
    } catch (e) {
      print('❌ 获取相机列表失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('获取相机列表失败: $e')));
      }
      return;
    }

    print('📷 正在初始化相机控制器...');
    _controller = CameraController(
      cameras[_currentCameraIndex], // 使用当前选择的摄像头
      ResolutionPreset.high,
      enableAudio: false, // 禁用音频
    );

    try {
      await _controller!.initialize();

      // 等待一小段时间确保相机完全准备好
      await Future.delayed(const Duration(milliseconds: 300));

      print('✅ 相机控制器初始化成功');

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
        print('🎉 相机界面已更新');
      }
    } catch (e) {
      print('❌ 相机控制器初始化错误: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('相机初始化失败: $e')));
      }
    }
  }

  Future<void> _takePicture() async {
    if (_controller == null ||
        !_controller!.value.isInitialized ||
        _isTakingPicture) {
      print('⚠️ 相机未准备好或正在拍照中');
      return;
    }

    // 检查相机状态
    if (_controller!.value.hasError) {
      print('❌ 相机状态异常，尝试重新初始化...');
      await _reinitializeCamera();
      return;
    }

    setState(() {
      _isTakingPicture = true;
    });

    try {
      print('📸 开始拍照...');
      print(
        '📸 相机状态: 已初始化=${_controller!.value.isInitialized}, 预览暂停=${_controller!.value.isPreviewPaused}',
      );

      // 确保预览没有暂停
      if (_controller!.value.isPreviewPaused) {
        print('📷 预览已暂停，恢复预览...');
        await _controller!.resumePreview();
        await Future.delayed(const Duration(milliseconds: 200));
      }

      // 拍照
      final XFile image = await _controller!.takePicture();
      print('📸 拍照完成: ${image.path}');

      // 添加水印并保存
      final String? savedPath = await _addWatermarkAndSave(image.path);

      if (savedPath != null) {
        print('💾 照片保存成功: $savedPath');
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('照片已保存到相册')));
        }
      } else {
        print('❌ 照片保存失败');
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('照片保存失败')));
        }
      }

      // 确保相机预览恢复正常
      await _ensureCameraPreview();
    } catch (e) {
      print('❌ 拍照错误: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('拍照失败: $e')));
      }

      // 发生错误时也要确保相机预览恢复
      await _ensureCameraPreview();
    } finally {
      if (mounted) {
        setState(() {
          _isTakingPicture = false;
        });
      }
      print('📸 拍照流程完成');
    }
  }

  // 确保相机预览正常工作
  Future<void> _ensureCameraPreview() async {
    try {
      if (_controller != null && _controller!.value.isInitialized) {
        // 检查预览是否正常
        if (!_controller!.value.isPreviewPaused) {
          print('📷 相机预览正常');
          return;
        }

        print('📷 相机预览暂停，尝试恢复...');

        // 在某些设备上，拍照后预览可能会暂停，需要重新启动
        await _controller!.pausePreview();
        await Future.delayed(const Duration(milliseconds: 100));
        await _controller!.resumePreview();

        print('📷 相机预览已恢复');
      }
    } catch (e) {
      print('⚠️ 恢复相机预览时出错: $e');
      // 如果恢复失败，尝试重新初始化相机
      await _reinitializeCamera();
    }
  }

  // 重新初始化相机
  Future<void> _reinitializeCamera() async {
    try {
      print('🔄 重新初始化相机...');

      // 确保先清理现有资源
      if (_controller != null) {
        try {
          await _controller!.dispose();
        } catch (e) {
          print('⚠️ 清理旧相机控制器时出错: $e');
        }
        _controller = null;
      }

      setState(() {
        _isInitialized = false;
      });

      // 等待一小段时间确保资源完全释放
      await Future.delayed(const Duration(milliseconds: 300));

      if (cameras.isNotEmpty) {
        _controller = CameraController(
          cameras[_currentCameraIndex],
          ResolutionPreset.high,
          enableAudio: false,
        );

        await _controller!.initialize();

        // 再等待一小段时间确保相机完全准备好
        await Future.delayed(const Duration(milliseconds: 200));

        if (mounted) {
          setState(() {
            _isInitialized = true;
          });
        }

        print('✅ 相机重新初始化成功');
      }
    } catch (e) {
      print('❌ 重新初始化相机失败: $e');
      // 如果重新初始化失败，再次尝试
      if (mounted) {
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted && !_isInitialized) {
            print('🔄 重试相机初始化...');
            _reinitializeCamera();
          }
        });
      }
    }
  }

  Future<String?> _addWatermarkAndSave(String imagePath) async {
    try {
      // 读取图片
      final bytes = await File(imagePath).readAsBytes();
      final img.Image? image = img.decodeImage(bytes);

      if (image == null) return null;

      // 添加水印
      final watermarkedImage = _addWatermark(image);

      // 获取真正的相册目录（DCIM/Camera）
      Directory? picturesDir;
      String savePath;

      if (Platform.isAndroid) {
        // Android: 在DCIM目录下创建专门的透卡相机文件夹
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          // 构建 DCIM/TransparentCard 路径（在相册中创建专门文件夹）
          final dcimPath = externalDir.path.replaceAll(
            '/Android/data/com.example.watermark_camera/files',
            '/DCIM/TransparentCard',
          );
          picturesDir = Directory(dcimPath);
          print('📁 保存目录: $dcimPath');
        }
      } else {
        // iOS: 在文档目录下创建透卡相机文件夹
        final documentsDir = await getApplicationDocumentsDirectory();
        picturesDir = Directory(
          path.join(documentsDir.path, 'TransparentCard'),
        );
      }

      if (picturesDir == null) {
        print('❌ 无法获取保存目录');
        return null;
      }

      // 确保目录存在
      if (!await picturesDir.exists()) {
        await picturesDir.create(recursive: true);
        print('📁 创建保存目录: ${picturesDir.path}');
      }

      // 生成文件名（使用更友好的格式）
      final now = DateTime.now();
      final formatter = DateFormat('yyyyMMdd_HHmmss');
      final fileName = 'TransparentCard_${formatter.format(now)}.jpg';
      savePath = path.join(picturesDir.path, fileName);

      print('💾 保存路径: $savePath');

      // 保存带水印的图片
      await File(savePath).writeAsBytes(img.encodeJpg(watermarkedImage));
      print('✅ 照片已保存到: $savePath');

      // 删除原始图片
      await File(imagePath).delete();

      return savePath;
    } catch (e) {
      print('添加水印错误: $e');
      return null;
    }
  }

  // 切换前后置摄像头
  Future<void> _switchCamera() async {
    if (_isSwitchingCamera || cameras.length < 2) return;

    setState(() {
      _isSwitchingCamera = true;
    });

    try {
      // 切换到下一个摄像头
      _currentCameraIndex = (_currentCameraIndex + 1) % cameras.length;

      print(
        '📷 切换到摄像头: ${cameras[_currentCameraIndex].name} (${cameras[_currentCameraIndex].lensDirection})',
      );

      // 重新初始化相机
      await _reinitializeCamera();
    } catch (e) {
      print('❌ 切换摄像头失败: $e');
      // 如果切换失败，回退到原来的摄像头
      _currentCameraIndex = (_currentCameraIndex - 1) % cameras.length;
    } finally {
      setState(() {
        _isSwitchingCamera = false;
      });
    }
  }

  img.Image _addWatermark(img.Image image) {
    // 如果选择了PNG水印，应用PNG水印
    final selectedWatermark = _watermarkManager.selectedWatermark;
    if (selectedWatermark != null) {
      // 获取当前屏幕尺寸作为预览尺寸参考
      final screenSize = MediaQuery.of(context).size;
      final previewRect = _watermarkManager.calculateCameraPreviewRect(
        screenSize,
        selectedWatermark,
      );

      return _watermarkManager.applyWatermarkToImage(
            image,
            selectedWatermark,
            previewSize: Size(previewRect.width, previewRect.height),
          ) ??
          image;
    }

    // 如果没有选择PNG水印，直接返回原图（不添加任何水印）
    return image;
  }

  // 构建相机预览组件（完整显示相机画面）
  Widget _buildCameraPreview() {
    // 始终全屏显示相机预览，确保用户看到完整的拍摄画面
    return SizedBox.expand(child: CameraPreview(_controller!));
  }

  // 构建PNG水印叠加组件（支持手势控制）
  Widget _buildPngWatermarkOverlay() {
    final selectedWatermark = _watermarkManager.selectedWatermark;
    if (selectedWatermark?.uiImage == null) return const SizedBox.shrink();

    final screenSize = MediaQuery.of(context).size;
    final watermarkRect = _watermarkManager.calculateWatermarkRect(
      screenSize,
      selectedWatermark!,
    );

    return Positioned(
      left: watermarkRect.left,
      top: watermarkRect.top,
      width: watermarkRect.width,
      height: watermarkRect.height,
      child: GestureDetector(
        onScaleStart: (details) {
          // 记录初始缩放和旋转值
          _initialScale = selectedWatermark.scale;
          _initialRotation = selectedWatermark.rotation;
        },
        onScaleUpdate: (details) {
          // 更新缩放
          final newScale = (_initialScale * details.scale).clamp(0.1, 3.0);
          _watermarkManager.updateWatermarkScale(newScale);

          // 更新旋转（如果有旋转手势）
          if (details.rotation != 0.0) {
            final newRotation = _initialRotation + details.rotation;
            _watermarkManager.updateWatermarkRotation(newRotation);
          }

          setState(() {});
        },
        child: CustomPaint(
          painter: WatermarkOverlayPainter(
            selectedWatermark.uiImage!,
            scale: selectedWatermark.scale,
            rotation: selectedWatermark.rotation,
            flipHorizontal: selectedWatermark.flipHorizontal,
            flipVertical: selectedWatermark.flipVertical,
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    print('📱 应用生命周期变化: $state');

    switch (state) {
      case AppLifecycleState.paused:
        print('📱 应用进入后台，释放相机资源');
        _isAppInBackground = true;
        if (_controller != null) {
          _controller!.dispose();
          _controller = null;
          setState(() {
            _isInitialized = false;
          });
        }
        break;
      case AppLifecycleState.resumed:
        print('📱 应用恢复前台，重新初始化相机');
        _isAppInBackground = false;
        // 总是重新初始化相机，确保从其他应用返回时正常工作
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted) {
            _reinitializeCamera();
          }
        });
        break;
      case AppLifecycleState.inactive:
        print('📱 应用变为非活跃状态');
        // 在inactive状态时也暂停相机预览
        if (_controller != null && _controller!.value.isInitialized) {
          _controller!.pausePreview();
        }
        break;
      case AppLifecycleState.detached:
        print('📱 应用分离');
        break;
      case AppLifecycleState.hidden:
        print('📱 应用隐藏');
        break;
    }
  }

  // 调用系统相机
  Future<void> _useSystemCamera() async {
    try {
      print('📷 调用系统相机...');

      // 暂停当前相机预览
      if (_controller != null && _controller!.value.isInitialized) {
        await _controller!.pausePreview();
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 90,
      );

      if (image != null) {
        print('📷 系统相机拍摄成功: ${image.path}');

        // 添加水印并保存
        final String? savedPath = await _addWatermarkAndSave(image.path);

        if (savedPath != null && mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('照片已保存到透卡相机相册')));
        }
      } else {
        print('📷 用户取消了系统相机拍摄');
      }

      // 恢复相机预览
      print('📷 系统相机调用完成，开始恢复相机...');
      await _restoreCameraAfterSystemCall();
    } catch (e) {
      print('❌ 系统相机调用失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('调用系统相机失败')));
      }

      // 即使出错也要尝试恢复相机
      await _restoreCameraAfterSystemCall();
    }
  }

  // 系统调用后恢复相机
  Future<void> _restoreCameraAfterSystemCall() async {
    try {
      print('🔄 恢复相机预览...');

      // 等待一小段时间确保系统相机完全释放资源
      await Future.delayed(const Duration(milliseconds: 500));

      if (_controller == null || !_controller!.value.isInitialized) {
        print('🔄 相机未初始化，重新初始化...');
        await _reinitializeCamera();
      } else {
        print('🔄 恢复相机预览...');
        await _controller!.resumePreview();

        // 再次检查相机状态
        if (_controller!.value.hasError) {
          print('🔄 相机状态异常，重新初始化...');
          await _reinitializeCamera();
        }
      }
    } catch (e) {
      print('❌ 恢复相机失败: $e');
      // 如果恢复失败，尝试重新初始化
      await _reinitializeCamera();
    }
  }

  // 打开相册页面
  void _openGallery() async {
    // 暂停相机预览
    if (_controller != null && _controller!.value.isInitialized) {
      await _controller!.pausePreview();
    }

    // 导航到相册页面
    if (mounted) {
      await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const GalleryPage()),
      );

      // 从相册返回后恢复相机
      await _restoreCameraAfterSystemCall();
    }
  }

  // 打开PNG水印菜单
  void _openPngMenu() async {
    // 防止重复点击
    if (_isLoadingMenu) return;

    setState(() {
      _isLoadingMenu = true;
    });

    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const Dialog(
            backgroundColor: Colors.transparent,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(color: Colors.white),
                  SizedBox(height: 16),
                  Text(
                    '正在加载水印菜单...',
                    style: TextStyle(color: Colors.white, fontSize: 16),
                  ),
                ],
              ),
            ),
          );
        },
      );

      // 暂停相机预览
      if (_controller != null && _controller!.value.isInitialized) {
        await _controller!.pausePreview();
      }

      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 导航到PNG菜单页面
      if (mounted) {
        await Navigator.push<PngWatermark?>(
          context,
          MaterialPageRoute(
            builder: (context) => PngMenuPage(cameraController: _controller),
          ),
        );

        // 无论是否选择了水印，都刷新界面以反映可能的缩放和旋转调整
        if (mounted) {
          setState(() {
            // 刷新界面以显示最新的水印设置
          });
        }

        // 从菜单返回后恢复相机
        await _restoreCameraAfterSystemCall();
      }
    } catch (e) {
      print('❌ 打开PNG菜单时出错: $e');
      // 确保关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMenu = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 相机预览
          if (_isInitialized && _controller != null)
            _buildCameraPreview()
          else
            const Center(child: CircularProgressIndicator(color: Colors.white)),

          // PNG水印叠加
          if (_watermarkManager.selectedWatermark?.uiImage != null)
            _buildPngWatermarkOverlay(),

          // 左上角菜单按钮
          Positioned(
            top: MediaQuery.of(context).padding.top + 20,
            left: 20,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _isLoadingMenu ? null : _openPngMenu,
                borderRadius: BorderRadius.circular(25),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: _isLoadingMenu
                        ? Colors.grey.withOpacity(0.7)
                        : Colors.black.withOpacity(0.7),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: _isLoadingMenu ? Colors.grey : Colors.white,
                      width: 2,
                    ),
                  ),
                  child: _isLoadingMenu
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : const Icon(Icons.layers, color: Colors.white, size: 24),
                ),
              ),
            ),
          ),

          // 底部操作栏
          Positioned(
            bottom: 20,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // 相册按钮
                GestureDetector(
                  onTap: _openGallery,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: const Icon(
                      Icons.photo_library,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                ),

                // 拍照按钮（中间，保持原有样式）
                GestureDetector(
                  onTap: _isTakingPicture ? null : _takePicture,
                  child: Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                      border: Border.all(color: Colors.white, width: 3),
                    ),
                    child: _isTakingPicture
                        ? const Padding(
                            padding: EdgeInsets.all(20),
                            child: CircularProgressIndicator(strokeWidth: 3),
                          )
                        : const Icon(
                            Icons.camera_alt,
                            size: 35,
                            color: Colors.black,
                          ),
                  ),
                ),

                // 摄像头切换按钮
                GestureDetector(
                  onTap: cameras.length > 1 && !_isSwitchingCamera
                      ? _switchCamera
                      : null,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: cameras.length > 1 ? Colors.white : Colors.grey,
                        width: 2,
                      ),
                    ),
                    child: _isSwitchingCamera
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : Icon(
                            Icons.flip_camera_ios,
                            color: cameras.length > 1
                                ? Colors.white
                                : Colors.grey,
                            size: 28,
                          ),
                  ),
                ),
              ],
            ),
          ),

          // 顶部栏
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).padding.top + 10,
                bottom: 10,
                left: 20,
                right: 20,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.black.withOpacity(0.7), Colors.transparent],
                ),
              ),
              child: const Text(
                '透卡相机',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// PNG水印叠加绘制器（支持缩放、旋转和镜像翻转）
class WatermarkOverlayPainter extends CustomPainter {
  final ui.Image image;
  final double scale;
  final double rotation;
  final bool flipHorizontal;
  final bool flipVertical;

  WatermarkOverlayPainter(
    this.image, {
    this.scale = 1.0,
    this.rotation = 0.0,
    this.flipHorizontal = false,
    this.flipVertical = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..filterQuality = FilterQuality.high;

    // 计算水印的基础尺寸（与图像处理逻辑保持一致）
    final watermarkAspectRatio = image.width / image.height;
    final previewAspectRatio = size.width / size.height;

    double baseWidth, baseHeight;

    // 根据预览区域和水印的宽高比决定基础尺寸
    if (watermarkAspectRatio > previewAspectRatio) {
      // 水印更宽，以预览区域宽度为基准
      baseWidth = size.width;
      baseHeight = baseWidth / watermarkAspectRatio;
    } else {
      // 水印更高或比例相近，以预览区域高度为基准
      baseHeight = size.height;
      baseWidth = baseHeight * watermarkAspectRatio;
    }

    // 应用用户设置的缩放
    final finalWidth = baseWidth * scale;
    final finalHeight = baseHeight * scale;

    // 计算居中位置
    final centerX = size.width / 2;
    final centerY = size.height / 2;

    canvas.save();

    // 移动到中心点
    canvas.translate(centerX, centerY);

    // 应用镜像翻转
    if (flipHorizontal || flipVertical) {
      canvas.scale(flipHorizontal ? -1.0 : 1.0, flipVertical ? -1.0 : 1.0);
    }

    // 应用旋转
    if (rotation != 0.0) {
      canvas.rotate(rotation);
    }

    // 绘制水印（以中心为原点）
    final destRect = Rect.fromLTWH(
      -finalWidth / 2,
      -finalHeight / 2,
      finalWidth,
      finalHeight,
    );

    final srcRect = Rect.fromLTWH(
      0,
      0,
      image.width.toDouble(),
      image.height.toDouble(),
    );

    canvas.drawImageRect(image, srcRect, destRect, paint);

    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is WatermarkOverlayPainter) {
      return oldDelegate.image != image ||
          oldDelegate.scale != scale ||
          oldDelegate.rotation != rotation ||
          oldDelegate.flipHorizontal != flipHorizontal ||
          oldDelegate.flipVertical != flipVertical;
    }
    return true;
  }
}
